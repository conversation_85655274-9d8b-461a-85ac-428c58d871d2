import { Router } from "express";
import crudDA, { regexEmptyKeyController, replaceEmptyKeyController } from "../da/crudDA";
import Reward from "../function/Reward";
import login from "../function/login";
import { JwtPayload } from "jsonwebtoken";
import admin from "firebase-admin";
import intergrationDA from "../da/integrationDA";
import { randomGID } from "../Ultis/convert";
import { Console } from "node:console";
const router = Router();
const _Login = new login();
const _reward = new Reward();
interface FCMRequest {
  noti: {
    title: string;
    body: string;
    imageUrl?: string; // Không bắt buộc
  };
  data: {
    id: string | number;
    type: string | number;
    url: string;
  };
  deviceToken?: string; // Chỉ dùng cho `sendMessageToDevice`
  deviceTokens?: string[]; // Chỉ dùng cho `sendMessageToGroup`
}
/**
 * @swagger
 * components:
 *   schemas:
 *     LoginRequest:
 *       type: object
 *       properties:
 *         type:
 *           type: string
 *           enum: [account, phone, apple, google, microsoft]
 *           description: Loại đăng nhập người dùng
 *         token:
 *           type: string
 *           description: Token xác thực cho đăng nhập bên thứ ba
 *         deviceToken:
 *           type: string
 *           description: Token thiết bị cho push notification
 *         ggClientId:
 *           type: string
 *           description: Google Client ID cho đăng nhập Google
 *         phone:
 *           type: string
 *           description: Số điện thoại người dùng
 *         password:
 *           type: string
 *           description: Mật khẩu người dùng
 *         email:
 *           type: string
 *           description: Email người dùng
 *       example:
 *         type: "account"
 *         phone: "**********"
 *         password: "password123"
 *
 *     TokenResponse:
 *       type: object
 *       properties:
 *         code:
 *           type: integer
 *           description: Mã trạng thái
 *         message:
 *           type: string
 *           description: Thông báo kết quả
 *         data:
 *           type: object
 *           properties:
 *             accessToken:
 *               type: string
 *               description: JWT access token
 *             refreshToken:
 *               type: string
 *               description: JWT refresh token
 *       example:
 *         code: 200
 *         message: "Success"
 *         data:
 *           accessToken: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
 *           refreshToken: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
 *
 *     ErrorResponse:
 *       type: object
 *       properties:
 *         code:
 *           type: integer
 *           description: Mã lỗi
 *         message:
 *           type: string
 *           description: Thông báo lỗi
 *       example:
 *         code: 401
 *         message: "Missing password"
 *
 *     CheckPasswordRequest:
 *       type: object
 *       properties:
 *         phone:
 *           type: string
 *           description: Số điện thoại người dùng
 *         password:
 *           type: string
 *           description: Mật khẩu cần kiểm tra
 *         email:
 *           type: string
 *           description: Email người dùng
 *       example:
 *         phone: "**********"
 *         password: "password123"
 *
 *     RefreshTokenRequest:
 *       type: object
 *       properties:
 *         refreshToken:
 *           type: string
 *           description: JWT refresh token
 *       example:
 *         refreshToken: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
 *
 *     ListQueryRequest:
 *       type: object
 *       properties:
 *         searchRaw:
 *           type: string
 *           description: Câu truy vấn tìm kiếm (xem thêm cú pháp tại https://www.notion.so/Redis-1b33d4d4b181800190d4e0b243f38c7a)
 *         page:
 *           type: integer
 *           description: Số trang
 *         size:
 *           type: integer
 *           description: Số lượng kết quả trên mỗi trang
 *         returns:
 *           type: array
 *           items:
 *             type: string
 *           description: Danh sách trường cần trả về
 *         sortby:
 *           type: string
 *           description: Trường sắp xếp
 *       example:
 *         searchRaw: "*"
 *         page: 1
 *         size: 10
 *         returns: ["Id", "Name", "Email"]
 *         sortby: { BY: "DateCreated", DIRECTION: "DESC" }
 *
 *     AggregateRequest:
 *       type: object
 *       properties:
 *         page:
 *           type: integer
 *           description: Số trang
 *         size:
 *           type: integer
 *           description: Số lượng kết quả trên mỗi trang
 *         sortby:
 *           type: string
 *           description: Trường sắp xếp
 *         searchRaw:
 *           type: string
 *           description: Câu truy vấn tìm kiếm (xem thêm cú pháp tại https://www.notion.so/Redis-1b33d4d4b181800190d4e0b243f38c7a)
 *         filter:
 *           type: string
 *           description: Bộ lọc bổ sung
 *         returns:
 *           type: array
 *           items:
 *             type: string
 *           description: Danh sách trường cần trả về
 *       example:
 *         page: 1
 *         size: 10
 *         sortby: [{prop: "DateCreated", direction: "DESC" }, {prop: "Sort", direction: "ASC" }]
 *         searchRaw: "*"
 *         filter: "APPLY exists(@Name) AS __exist FILTER (@__exist == 1)"
 *         returns: ["Id", "Name", "Email"]
 *
 *     PatternRequest:
 *       type: object
 *       properties:
 *         page:
 *           type: integer
 *           description: Số trang
 *         size:
 *           type: integer
 *           description: Số lượng kết quả trên mỗi trang
 *         sortby:
 *           type: string
 *           description: Trường sắp xếp
 *         searchRaw:
 *           type: string
 *           description: Câu truy vấn tìm kiếm (xem thêm cú pháp tại https://www.notion.so/Redis-1b33d4d4b181800190d4e0b243f38c7a)
 *         pattern:
 *           type: object
 *           properties:
 *             returns:
 *               type: array
 *               items:
 *                 type: string
 *               description: Danh sách trường cần trả về của module này
 *             RelativeKey:
 *               type: array
 *               items:
 *                 type: string
 *               description: Khoa chính cần trả về
 *             FKKey:
 *               type: object
 *               properties:
 *                 searchRaw:
 *                   type: string
 *                   description: Câu truy vấn tìm kiếm (xem thêm cú pháp tại https://www.notion.so/Redis-1b33d4d4b181800190d4e0b243f38c7a)
 *                 reducers:
 *                   type: string
 *                   description: Câu truy vấn GROUPBY AGGREGATE (xem thêm cú pháp tại https://www.notion.so/Redis-1b33d4d4b181800190d4e0b243f38c7a)
 *       example:
 *         page: 1
 *         size: 10
 *         sortby: [{prop: "DateCreated", direction: "DESC" }, {prop: "Sort", direction: "ASC" }]
 *         searchRaw: "*"
 *         filter: "APPLY exists(@Name) AS __exist FILTER (@__exist == 1)"
 *         pattern: { returns: ["Id", "Name", "DateCreated", "Type"], CustomerId: ["Id", "Name", "Email"], Lesson: { searchRaw: "*", reducers: "GROUPBY 1 @CourseId REDUCE COUNT 0 AS total" } }
 *
 *     OTPRequest:
 *       type: object
 *       properties:
 *         phone:
 *           type: string
 *           description: Số điện thoại nhận OTP
 *         expiresIn:
 *           type: integer
 *           description: Thời gian hết hạn (milliseconds)
 *       example:
 *         phone: "**********"
 *         expiresIn: 600000
 *
 *     VerifyOTPRequest:
 *       type: object
 *       properties:
 *         phone:
 *           type: string
 *           description: Số điện thoại
 *         otp:
 *           type: string
 *           description: Mã OTP
 *       example:
 *         phone: "**********"
 *         otp: "123456"
 *
 *     GroupRequest:
 *       type: object
 *       properties:
 *         reducers:
 *           type: string
 *           description: Câu truy vấn reducer
 *         searchRaw:
 *           type: string
 *           description: Câu truy vấn tìm kiếm
 *       example:
 *         reducers: "GROUPBY 1 @Category REDUCE COUNT 0 AS total"
 *         searchRaw: "*"
 *
 *     EvalRequest:
 *       type: object
 *       properties:
 *         script:
 *           type: string
 *           description: Script cần thực thi
 *         keys:
 *           type: array
 *           items:
 *             type: string
 *           description: Danh sách keys
 *         args:
 *           type: array
 *           items:
 *             type: string
 *           description: Danh sách tham số
 *       example:
 *         script: "return redis.call('get', KEYS[1])"
 *         keys: ["user:1"]
 *         args: []
 *
 *     ActionRequest:
 *       type: object
 *       properties:
 *         data:
 *           type: array
 *           items:
 *             type: object
 *           description: Dữ liệu cần xử lý
 *         ids:
 *           type: array
 *           items:
 *             type: string
 *           description: Danh sách ID
 *       example:
 *         data: [{"Id": "123", "Name": "User 1"}]
 *         ids: ["123", "456"]
 *
 *   parameters:
 *     pidHeader:
 *       in: header
 *       name: pid
 *       schema:
 *         type: string
 *       required: true
 *       description: ID của project
 *     moduleHeader:
 *       in: header
 *       name: module
 *       schema:
 *         type: string
 *       required: true
 *       description: Tên module
 *     authorizationHeader:
 *       in: header
 *       name: authorization
 *       schema:
 *         type: string
 *       required: true
 *       description: Bearer token xác thực
 *
 */
/**
 * @swagger
 * /data/login:
 *   post:
 *     summary: Đăng nhập hoặc đăng ký tài khoản mới
 *     description: Hỗ trợ nhiều phương thức đăng nhập bao gồm tài khoản, số điện thoại, Google, Apple, Microsoft
 *     tags: [Authentication]
 *     parameters:
 *       - $ref: '#/components/parameters/pidHeader'
 *       - $ref: '#/components/parameters/moduleHeader'
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/LoginRequest'
 *     responses:
 *       200:
 *         description: Đăng nhập thành công
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/TokenResponse'
 *       401:
 *         description: Thiếu thông tin đăng nhập
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       403:
 *         description: Mật khẩu không đúng
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       404:
 *         description: Không tìm thấy project
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.post("/login", async (req, res) => {
  const { pid, module } = req.headers;
  const _moduleRepo = new crudDA(`data:${pid}:${module}`);
  const { type, token, deviceToken, ggClientId, phone, password, email, username } = req.body;
  if (type === "account") {
    if (!password) return res.send({ code: 401, message: "Missing password" });
    if (!phone && !email && !username) return res.send({ code: 401, message: "Missing phone or email or username" });
    if (phone) {
      const userLogin = (await _moduleRepo.search(1, 1, `@Mobile:("${phone}")`)) as any;
      var userLoginData = userLogin.data?.[0];
      if (!userLogin) return res.send({ code: 401, message: "Phone number is not registered" });
    } else if (email) {
      const userLogin = (await _moduleRepo.search(1, 1, `@Email:("${email}")`)) as any;
      userLoginData = userLogin.data?.[0];
      if (!userLogin) return res.send({ code: 401, message: "Email is not registered" });
    } else {
      const userLogin = (await _moduleRepo.search(1, 1, `@UserName:("${username}")`)) as any;
      userLoginData = userLogin.data?.[0];
      if (!userLogin) return res.send({ code: 401, message: "Username is not registered" });
    }
    const isMatch = await _Login.verifyPassword(password, userLoginData.Password ?? "");
    if (!isMatch) {
      return res.send({ code: 403, message: "Invalid password" });
    } else {
      const payload = { id: userLoginData.Id, mobile: userLoginData.Mobile };
      const accessToken = _Login.createAccessToken(payload);
      const refreshToken = _Login.createRefreshToken(payload);
      return res.status(200).json({ data: { accessToken, refreshToken }, code: 200, message: "Success" });
    }
  } else if (type === "phone" && phone) {
    const userLogin = (await _moduleRepo.search(1, 1, `@Mobile:("${phone}")`, { RETURN: ["Id", "UserName", "Mobile"] })) as any;
    if (userLogin.count) {
      const payload = { id: userLogin.data[0].Id, mobile: userLogin.data[0].Mobile };
      const accessToken = _Login.createAccessToken(payload);
      const refreshToken = _Login.createRefreshToken(payload);
      return res.status(200).json({ data: { accessToken, refreshToken }, code: 200, message: "Success" });
    } else {
      const newCustomer = {
        Id: randomGID(),
        Name: phone,
        DateCreated: Date.now(),
        Mobile: phone,
        Status: 1,
      };
      await _moduleRepo.action("add", [newCustomer]);
      const accessToken = _Login.createAccessToken({ id: newCustomer.Id, email: newCustomer.Mobile });
      const refreshToken = _Login.createRefreshToken({ id: newCustomer.Id, email: newCustomer.Mobile });
      return res.status(200).json({ code: 200, message: "Success", data: { accessToken, refreshToken } });
    }
  } else if (pid) {
    const integration = new intergrationDA(pid as string);
    const _projectRepo = new crudDA(`wini:Project`);
    const projectData = (await _projectRepo.getById(pid as string)) as any;
    if (!projectData) return res.send({ code: 404, message: "Project not found" });
    const response = await integration.login({ type, token, deviceToken, ggClientId, clientSecret: projectData.ClientSecret });
    if (response.code === 200 && response.data?.email) {
      const payload = response.data;
      const checkEmail = await _moduleRepo.search(1, 1, `@Email:("${payload.email}")`);
      let _customer: any = {};
      switch (type) {
        case "apple":
          _customer.IdUserApple = payload.sub ?? "";
          break;
        case "google":
          _customer.IdUserGoogle = payload.sub ?? "";
          break;
        case "microsoft":
          _customer.IdUserMicrsoft = payload.sub ?? "";
          break;
        default:
          break;
      }
      if (checkEmail.count) {
        _customer = { ...checkEmail.data[0], ..._customer };
        if (deviceToken) {
          _customer.DeviceToken = checkEmail.data[0].DeviceToken
            ? [
                ...checkEmail.data[0].DeviceToken.split(",")
                  .slice(1)
                  .filter((tk: string) => tk !== deviceToken),
                deviceToken,
              ].join(",")
            : deviceToken;
        }
      } else {
        _customer = {
          ..._customer,
          Id: randomGID(),
          Email: payload.email,
          Name: payload.name,
          AvatarUrl: payload.picture,
          DateCreated: Date.now(),
          DeviceToken: deviceToken,
        };
      }
      await _moduleRepo.action("add", [_customer]);
      const accessToken = _Login.createAccessToken({ id: _customer.Id, email: payload.Email });
      const refreshToken = _Login.createRefreshToken({ id: _customer.Id, email: payload.Email });
      return res.status(200).json({ code: 200, message: "Success", data: { accessToken, refreshToken } });
    } else return res.send(response);
  }
  return res.send({ code: 404, message: "Missing pid" });
});

/**
 * @swagger
 * /data/checkPassword:
 *   post:
 *     summary: Kiểm tra tính hợp lệ của mật khẩu
 *     description: Xác minh mật khẩu của người dùng thông qua email hoặc số điện thoại
 *     tags: [Authentication]
 *     parameters:
 *       - $ref: '#/components/parameters/pidHeader'
 *       - $ref: '#/components/parameters/moduleHeader'
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CheckPasswordRequest'
 *     responses:
 *       200:
 *         description: Mật khẩu hợp lệ
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: "Valid password"
 *       401:
 *         description: Thiếu thông tin đăng nhập
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       403:
 *         description: Mật khẩu không đúng
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.post("/checkPassword", async (req, res) => {
  const { pid, module } = req.headers;
  const _moduleRepo = new crudDA(`data:${pid}:${module}`);
  const { phone, password, email, username } = req.body;
  if (!password) return res.send({ code: 401, message: "Missing password" });
  if (!phone && !email && !username) return res.send({ code: 401, message: "Missing phone or email or username" });
  if (phone) {
    const userLogin = (await _moduleRepo.search(1, 1, `@Mobile:("${phone}")`)) as any;
    var userLoginData = userLogin.data?.[0];
    if (!userLoginData) return res.send({ code: 401, message: "Phone number is not registered" });
  } else if (email) {
    const userLogin = (await _moduleRepo.search(1, 1, `@Email:("${email}")`)) as any;
    userLoginData = userLogin.data?.[0];
    if (!userLoginData) return res.send({ code: 401, message: "Email is not registered" });
  } else {
    const userLogin = (await _moduleRepo.search(1, 1, `@UserName:("${username}")`)) as any;
    userLoginData = userLogin.data?.[0];
    if (!userLogin) return res.send({ code: 401, message: "Username is not registered" });
  }
  const isMatch = await _Login.verifyPassword(password, userLoginData.Password ?? "");
  if (!isMatch) {
    return res.send({ code: 403, message: "Invalid password" });
  } else {
    return res.status(200).json({ code: 200, message: "Valid password" });
  }
});
//push notification
/**
 * @swagger
 * /data/sendNotification:
 *   post:
 *     summary: Gửi thông báo
 *     description: Gửi thông báo đến thiết bị của người dùng
 *     tags: [Notification]
 *     parameters:
 *       - $ref: '#/components/parameters/pidHeader'
 *       - $ref: '#/components/parameters/moduleHeader'
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/SendNotificationRequest'
 *     responses:
 *       200:
 *         description: Gửi thông báo thành công
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: "Success"
 *       404:
 *         description: Không tìm thấy người dùng
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.post("/sendNotification", async (req, res) => {
  const { pid } = req.headers;
  const { data } = req.body as FCMRequest;
  const integration = new intergrationDA(pid as string);
  const response = await integration.sendMessageToGroup(data as any);
  if (response.code === 200) {
    return res.status(200).json({ code: 200, message: "Success" });
  } else return res.send(response);
});

/**
 * @swagger
 * /data/checkPassword:
 *   post:
 *     summary: Kiểm tra tính hợp lệ của mật khẩu
 *     description: Xác minh mật khẩu của người dùng thông qua email hoặc số điện thoại
 *     tags: [Authentication]
 *     parameters:
 *       - $ref: '#/components/parameters/pidHeader'
 *       - $ref: '#/components/parameters/moduleHeader'
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CheckPasswordRequest'
 *     responses:
 *       200:
 *         description: Mật khẩu hợp lệ
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: "Valid password"
 *       401:
 *         description: Thiếu thông tin đăng nhập
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 *       403:
 *         description: Mật khẩu không đúng
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.post("/checkPassword", async (req, res) => {
  const { pid, module } = req.headers;
  const _moduleRepo = new crudDA(`data:${pid}:${module}`);
  const { phone, password, email } = req.body;
  if (!password) return res.send({ code: 401, message: "Missing password" });
  if (!phone && !email) return res.send({ code: 401, message: "Missing phone or email" });
  if (phone) {
    const userLogin = (await _moduleRepo.search(1, 1, `@Mobile:("${phone}")`)) as any;
    var userLoginData = userLogin.data?.[0];
    if (!userLogin) return res.send({ code: 401, message: "Phone number is not registered" });
  } else {
    const userLogin = (await _moduleRepo.search(1, 1, `@Email:{"${email}"}`)) as any;
    userLoginData = userLogin.data?.[0];
    if (!userLogin) return res.send({ code: 401, message: "Email is not registered" });
  }
  const isMatch = await _Login.verifyPassword(password, userLoginData.Password ?? "");
  if (!isMatch) {
    return res.send({ code: 403, message: "Invalid password" });
  } else {
    return res.status(200).json({ code: 200, message: "Valid password" });
  }
});

/**
 * @swagger
 * /data/bcrypt:
 *   get:
 *     summary: Mã hóa mật khẩu
 *     description: Tạo chuỗi mật khẩu đã mã hóa bằng bcrypt
 *     tags: [Authentication]
 *     parameters:
 *       - in: query
 *         name: password
 *         required: true
 *         schema:
 *           type: string
 *         description: Mật khẩu cần mã hóa
 *     responses:
 *       200:
 *         description: Mã hóa thành công
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: "Success"
 *                 data:
 *                   type: string
 *                   example: "$2b$10$X7SLU7CrUX.4Ojm6h0ucTuDWYX4jyAtMmA2n2QsKxNxQX4bI9L3Bm"
 *       404:
 *         description: Thiếu mật khẩu
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.get("/bcrypt", async (req, res) => {
  const { password } = req.query;
  if (password) {
    const result = await _Login.hashPassword(password as string);
    return res.send({ code: 200, message: "Success", data: result });
  }
  return res.send({ code: 404, message: "Missing password" });
});

/**
 * @swagger
 * /data/getInfo:
 *   get:
 *     summary: Lấy thông tin người dùng
 *     description: Lấy thông tin người dùng dựa trên token
 *     tags: [Users]
 *     parameters:
 *       - $ref: '#/components/parameters/pidHeader'
 *       - $ref: '#/components/parameters/moduleHeader'
 *       - $ref: '#/components/parameters/authorizationHeader'
 *     responses:
 *       200:
 *         description: Lấy thông tin thành công
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: "Success"
 *                 data:
 *                   type: object
 *                   description: Thông tin người dùng
 */
router.get("/getInfo", async (req, res) => {
  const { authorization, pid, module } = req.headers;
  const _moduleRepo = new crudDA(`data:${pid}:${module}`);
  const payload = _Login.verifyToken((authorization ?? "").replace("Bearer", "").trim()) as JwtPayload;
  const customer = await _moduleRepo.getById(payload.id);
  return res.status(200).json({ code: 200, message: "Success", data: customer });
});

/**
 * @swagger
 * /data/refreshToken:
 *   post:
 *     summary: Làm mới token
 *     description: Tạo access token mới từ refresh token
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/RefreshTokenRequest'
 *     responses:
 *       200:
 *         description: Làm mới token thành công
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/TokenResponse'
 *       401:
 *         description: Refresh token không hợp lệ
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.post("/refreshToken", async (req, res) => {
  const { refreshToken } = req.body;
  const response = await _Login.refreshToken(refreshToken);
  return res.send(response);
});

/**
 * @swagger
 * /data/getAll:
 *   get:
 *     summary: Lấy tất cả dữ liệu
 *     description: Lấy tất cả bản ghi từ module được chỉ định
 *     tags: [Data]
 *     parameters:
 *       - $ref: '#/components/parameters/pidHeader'
 *       - $ref: '#/components/parameters/moduleHeader'
 *     responses:
 *       200:
 *         description: Lấy dữ liệu thành công
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: "Success"
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                   description: Danh sách dữ liệu
 */
router.get("/getAll", async (req, res) => {
  const { pid, module } = req.headers;
  const _moduleRepo = new crudDA(`data:${pid}:${module}`);
  const results = await _moduleRepo.getAll();
  return res.status(200).json({ data: results, code: 200, message: "Success" });
});

/**
 * @swagger
 * /data/getListSimple:
 *   post:
 *     summary: Tìm kiếm dữ liệu với bộ lọc
 *     description: Tìm kiếm và phân trang dữ liệu với các tùy chọn
 *     tags: [Data]
 *     parameters:
 *       - $ref: '#/components/parameters/pidHeader'
 *       - $ref: '#/components/parameters/moduleHeader'
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/ListQueryRequest'
 *     responses:
 *       200:
 *         description: Tìm kiếm thành công
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: "Success"
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                   description: Danh sách kết quả
 *                 totalCount:
 *                   type: integer
 *                   description: Tổng số bản ghi
 */
router.post("/getListSimple", async (req, res) => {
  const { pid, module } = req.headers;
  if (module) {
    var _moduleRepo = new crudDA(`data:${pid}:${module}`);
  } else {
    _moduleRepo = new crudDA(`data`);
  }
  const { searchRaw, page, size, returns, sortby } = req.body;
  const results = await _moduleRepo.search(page, size, searchRaw, { RETURN: returns, SORTBY: sortby });
  if (results.code !== 404) {
    return res.status(200).json({ data: results.data, totalCount: results.count, code: 200, message: "Success" });
  } else return res.send(results);
});

/**
 * @swagger
 * /data/aggregateList:
 *   post:
 *     summary: Truy vấn dữ liệu tổng hợp
 *     description: Thực hiện truy vấn tổng hợp dữ liệu với các bộ lọc và tùy chọn phân trang
 *     tags: [Data]
 *     parameters:
 *       - $ref: '#/components/parameters/pidHeader'
 *       - $ref: '#/components/parameters/moduleHeader'
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/AggregateRequest'
 *     responses:
 *       200:
 *         description: Truy vấn thành công
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: "Success"
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                   description: Kết quả truy vấn
 *                 totalCount:
 *                   type: integer
 *                   description: Tổng số bản ghi
 */
router.post("/aggregateList", async (req, res) => {
  const { pid, module } = req.headers;
  const { page, size, sortby, searchRaw, filter, returns } = req.body;
  if (regexEmptyKeyController.test(searchRaw)) {
    const firstEmptyKey = regexEmptyKeyController.exec(searchRaw)!;
    const notEmpty = firstEmptyKey[0].includes("notempty");
    const key = firstEmptyKey[1];
    let _searchRaw = searchRaw.replace(replaceEmptyKeyController, "").trim();
    if (!_searchRaw.length) _searchRaw = `*`;
    const response = await aggregateData({ pid: pid as string, module: module as string, searchRaw: _searchRaw, filter: `APPLY exists(@${key}) AS __exist FILTER (@__exist == ${notEmpty ? 1 : 0})`, page, size, sortby, returns });
    if (response.totalCount) {
      const _moduleRepo = new crudDA(`data:${pid}:${module}`);
      const totalRes = await _moduleRepo.aggregate({ searchRaw: _searchRaw, query: `LOAD * APPLY exists(@${key}) AS __exist FILTER (@__exist == ${notEmpty ? 1 : 0}) GROUPBY 1 @__exist REDUCE COUNT 0 AS _totalCount` });
      const newTotalCount = totalRes[1]?.pop();
      return res.status(200).send({
        data: response.data.map((e: any) => {
          const tmp = { ...e };
          delete tmp.__exist;
          return tmp;
        }),
        totalCount: newTotalCount ? parseInt(newTotalCount) : response.totalCount,
        code: 200,
        message: "Success",
      });
    } else return res.send(response);
  } else {
    const response = await aggregateData({ pid: pid as string, module: module as string, page, size, sortby, searchRaw, filter, returns });
    return res.status(200).send(response);
  }
});

/**
 * @swagger
 * /data/patternList:
 *   post:
 *     summary: Truy vấn dữ liệu theo yêu cầu.
 *     description: Truy vấn theo yêu cầu truyền vào pattern ở body
 *     tags: [Data]
 *     parameters:
 *       - $ref: '#/components/parameters/pidHeader'
 *       - $ref: '#/components/parameters/moduleHeader'
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/PatternRequest'
 *     responses:
 *       200:
 *         description: Truy vấn thành công
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: "Success"
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                   description: Kết quả truy vấn
 *                 totalCount:
 *                   type: integer
 *                   description: Tổng số bản ghi
 *
 * */
router.post("/patternList", async (req, res) => {
  const { pid, module } = req.headers;
  const { page, size, sortby, searchRaw, filter, pattern } = req.body;
  if (!pid || !module) return res.send({ code: 404, message: "Missing pid or module" });
  let getModuleData: { data: Array<any>; totalCount: number; [p: string]: any } = { data: [], totalCount: 0 };
  // lấy data của bảng module theo aggregate
  if (regexEmptyKeyController.test(searchRaw)) {
    const firstEmptyKey = regexEmptyKeyController.exec(searchRaw)!;
    const notEmpty = firstEmptyKey[0].includes("notempty");
    const key = firstEmptyKey[1];
    let _searchRaw = searchRaw.replace(replaceEmptyKeyController, "").trim();
    if (!_searchRaw.length) _searchRaw = `*`;
    const response = await aggregateData({ pid: pid as string, module: module as string, searchRaw: _searchRaw, filter: `APPLY exists(@${key}) AS __exist FILTER (@__exist == ${notEmpty ? 1 : 0})`, returns: pattern?.returns, page, size, sortby });
    if (response.totalCount) {
      const _moduleRepo = new crudDA(`data:${pid}:${module}`);
      const totalRes = await _moduleRepo.aggregate({ searchRaw: _searchRaw, query: `LOAD * APPLY exists(@${key}) AS __exist FILTER (@__exist == ${notEmpty ? 1 : 0}) GROUPBY 1 @__exist REDUCE COUNT 0 AS _totalCount` });
      const newTotalCount = totalRes[1]?.pop();
      getModuleData = { data: response.data, totalCount: newTotalCount ? parseInt(newTotalCount) : response.totalCount };
    } else return res.send(response);
  } else {
    const response = await aggregateData({ pid: pid as string, module: module as string, returns: pattern?.returns, page, size, sortby, searchRaw, filter });
    if (response.code !== 200) return res.status(200).send(response);
    getModuleData = { data: response.data, totalCount: response.totalCount };
  }
  if (pattern) {
    // từ pattern lấy danh sách các bảng PK và FK cần xử lý
    const pkModules: { [p: string]: Array<string> } = {};
    const fkModules: { [p: string]: { searchRaw?: string; reducers: string } } = {};
    Object.keys(pattern).forEach((p: string) => {
      if (p !== "returns") {
        if (p.endsWith("Id")) {
          if (!pattern?.returns?.length || pattern.returns.includes(p)) pkModules[p] = pattern[p];
        } else fkModules[p] = pattern[p];
      }
    });
    // khai báo relativeData: đây là biến lưu các dữ liệu được redis trả về theo bảng PK và FK đã lấy ra ở trên
    let relativeData: Array<any> = [];
    let tableName: Array<string> = [];
    // gộp tất cả promise getByListId của bảng PK và relativeData
    if (Object.keys(pkModules).length) {
      tableName = Object.keys(pkModules).map((e) => e.substring(0, e.lastIndexOf("Id")));
      relativeData.push(
        ...Object.keys(pkModules).map((e, i) => {
          const isParent = e === "ParentId" || tableName[i] === module;
          const mRepo = new crudDA(`data:${pid}:${isParent ? module : tableName[i]}`);
          return mRepo.getBylistId(
            getModuleData.data
              .map((item: any) => (isParent ? item.ParentId : item[e]?.split(",")))
              .flat(Infinity)
              .filter((id: string, i: number, arr: Array<string>) => !!id?.length && arr.indexOf(id) === i)
          );
        })
      );
    }
    // gộp tất cả promise group của bảng FK và relativeData
    if (Object.keys(fkModules).length) {
      const fkSearch = `@${module}Id:{${getModuleData.data.map((e: any) => e.Id).join(" | ")}}`;
      relativeData.push(
        ...Object.keys(fkModules).map((e) => {
          let parentSearch: string | undefined;
          if (e === module) parentSearch = `@ParentId:{${getModuleData.data.map((e: any) => e.Id).join(" | ")}}`;
          const efkSearch = fkModules[e].searchRaw?.length ? fkModules[e].searchRaw : "*";
          return mapGroupData(pid as string, e, fkModules[e].reducers, efkSearch !== "*" ? `${parentSearch ?? fkSearch} ${efkSearch}` : parentSearch ?? fkSearch);
        })
      );
    }
    // xử lý dữ liệu mà promise trả ra
    if (relativeData.length) {
      relativeData = await Promise.all(relativeData);
      relativeData.slice(0, tableName.length).forEach((relativeResponse: any, i: number) => {
        getModuleData[tableName[i]] = relativeResponse
          .filter((e: any) => e !== undefined && e !== null)
          .map((e: any) => {
            const tmp: any = {};
            tmp.Id = e.Id;
            pkModules[`${tableName[i]}Id`].forEach((pkProps) => (tmp[pkProps] = e[pkProps]));
            return tmp;
          });
      });
      const fkRelativeData = relativeData
        .slice(tableName.length)
        .filter((e) => e.code === 200 && e.data.length)
        .map((e) => e.data)
        .flat(Infinity);
      getModuleData.data = getModuleData.data.map((e) => {
        let tmpE = { ...e };
        fkRelativeData
          .filter((fkE) => (fkE[`${module}Id`] ?? fkE.ParentId)?.includes(e.Id))
          .forEach((fkE) => {
            const tmpFKE = { ...fkE };
            delete tmpFKE[`${module}Id`];
            delete tmpFKE.ParentId;
            tmpE = { ...tmpE, ...tmpFKE };
          });
        return tmpE;
      });
    }
  }
  return res.status(200).send({ ...getModuleData, code: 200, message: "Success" });
});

router.post("/filterByEmptyKey", async (req, res) => {
  const { pid, module } = req.headers;
  const { page, size, sortby, searchRaw, key, notEmpty } = req.body;
  const _moduleRepo = new crudDA(`data:${pid}:${module}`);
  const response = await aggregateData({ pid: pid as string, module: module as string, page: page, size: size, sortby: sortby, searchRaw: searchRaw, filter: `APPLY exists(@${key}) AS __exist FILTER (@__exist == ${notEmpty ? 1 : 0})` });
  if (response.totalCount) {
    const totalRes = await _moduleRepo.aggregate({ searchRaw: searchRaw, query: `LOAD * APPLY exists(@${key}) AS __exist FILTER (@__exist == ${notEmpty ? 1 : 0}) GROUPBY 1 @__exist REDUCE COUNT 0 AS _totalCount` });
    const newTotalCount = totalRes[1]?.pop();
    return res.status(200).send({ data: response.data, totalCount: newTotalCount ? parseInt(newTotalCount) : response.totalCount, code: 200, message: "Success" });
  } else return res.send(response);
});

/**
 * @swagger
 * /data/send-otp:
 *   post:
 *     summary: Gửi mã OTP
 *     description: Gửi mã OTP đến số điện thoại
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/OTPRequest'
 *     responses:
 *       200:
 *         description: Gửi OTP thành công
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "OTP sent successfully!"
 *                 sessionInfo:
 *                   type: string
 *                   description: Thông tin phiên
 *       400:
 *         description: Thiếu số điện thoại
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Phone number is required."
 *       500:
 *         description: Lỗi khi gửi OTP
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Failed to send OTP."
 *                 error:
 *                   type: string
 *                   description: Thông báo lỗi
 */
router.post("/send-otp", async (req, res) => {
  const { phone, expiresIn } = req.body;

  if (!phone) {
    return res.status(400).json({ message: "Phone number is required." });
  }

  try {
    // Firebase does not send OTP directly through Admin SDK. You need to implement client-side
    const sessionInfo = await admin.auth().createSessionCookie(phone, {
      expiresIn: expiresIn ?? 10 * 60 * 1000, // 30s validity
    });

    // Return a session cookie or session info to the client
    return res.status(200).json({
      message: "OTP sent successfully!",
      sessionInfo,
    });
  } catch (error: any) {
    console.error("Error sending OTP:", error);
    return res.status(500).json({
      message: "Failed to send OTP.",
      error: error.message,
    });
  }
});

/**
 * @swagger
 * /data/check-otp:
 *   post:
 *     summary: Xác minh mã OTP
 *     description: Kiểm tra tính hợp lệ của mã OTP
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/VerifyOTPRequest'
 *     responses:
 *       200:
 *         description: OTP hợp lệ
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "OTP Verified Successfully!"
 *       400:
 *         description: Thiếu thông tin
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Phone number and OTP are required."
 *       401:
 *         description: OTP không hợp lệ
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "Invalid OTP or Phone Number!"
 *       500:
 *         description: Lỗi xác minh OTP
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 message:
 *                   type: string
 *                   example: "OTP Verification Failed."
 *                 error:
 *                   type: string
 *                   description: Thông báo lỗi
 */
router.post("/check-otp", async (req, res) => {
  const { phone, otp } = req.body;

  if (!phone || !otp) {
    return res.status(400).json({ message: "Phone number and OTP are required." });
  }

  try {
    // Verify the OTP using Firebase
    const verificationResult = await admin.auth().verifyIdToken(otp);

    if (verificationResult.phone_number === phone) {
      return res.status(200).json({ message: "OTP Verified Successfully!" });
    } else {
      return res.status(401).json({ message: "Invalid OTP or Phone Number!" });
    }
  } catch (error: any) {
    console.error("Error verifying OTP:", error);
    return res.status(500).json({ message: "OTP Verification Failed.", error: error.message });
  }
});

/**
 * @swagger
 * /data/group:
 *   post:
 *     summary: Nhóm dữ liệu
 *     description: Thực hiện truy vấn nhóm dữ liệu
 *     tags: [Data]
 *     parameters:
 *       - $ref: '#/components/parameters/pidHeader'
 *       - $ref: '#/components/parameters/moduleHeader'
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/GroupRequest'
 *     responses:
 *       200:
 *         description: Truy vấn thành công
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: "Success"
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                   description: Kết quả truy vấn
 *                 totalCount:
 *                   type: integer
 *                   description: Tổng số bản ghi
 *       404:
 *         description: Lỗi truy vấn
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.post("/group", async (req, res) => {
  const { pid, module } = req.headers;
  const { reducers, searchRaw } = req.body;
  const response = await mapGroupData(pid as string, module as string, reducers, searchRaw);
  return res.send(response);
});

/**
 * @swagger
 * /data/getById:
 *   post:
 *     summary: Lấy dữ liệu theo ID
 *     description: Tìm và trả về dữ liệu theo ID
 *     tags: [Data]
 *     parameters:
 *       - $ref: '#/components/parameters/pidHeader'
 *       - $ref: '#/components/parameters/moduleHeader'
 *       - in: query
 *         name: id
 *         schema:
 *           type: string
 *         required: true
 *         description: ID của bản ghi cần lấy
 *     responses:
 *       200:
 *         description: Lấy dữ liệu thành công
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: "Success"
 *                 data:
 *                   type: object
 *                   description: Dữ liệu bản ghi
 *       404:
 *         description: Không tìm thấy ID
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.post("/getById", async (req, res) => {
  const { pid, module } = req.headers;
  const { id } = req.query;
  const _moduleRepo = new crudDA(`data:${pid}:${module}`);
  if (id) {
    const results = await _moduleRepo.getById(id as string);
    return res.status(200).json({ data: results, code: 200, message: "Success" });
  } else {
    return res.send({ code: 404, message: "Missing id" });
  }
});

/**
 * @swagger
 * /data/getByIds:
 *   post:
 *     summary: Lấy nhiều bản ghi theo danh sách ID
 *     description: Tìm và trả về nhiều bản ghi theo danh sách ID
 *     tags: [Data]
 *     parameters:
 *       - $ref: '#/components/parameters/pidHeader'
 *       - $ref: '#/components/parameters/moduleHeader'
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               ids:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: Danh sách ID cần lấy
 *             example:
 *               ids: ["123", "456"]
 *     responses:
 *       200:
 *         description: Lấy dữ liệu thành công
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: "Success"
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                   description: Danh sách bản ghi
 *       404:
 *         description: Thiếu danh sách ID
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.post("/getByIds", async (req, res) => {
  const { pid, module } = req.headers;
  const { ids } = req.body;
  const _moduleRepo = new crudDA(`data:${pid}:${module}`);
  if (ids) {
    const results = await _moduleRepo.getBylistId(ids as any);
    return res.status(200).json({ data: results, code: 200, message: "Success" });
  } else {
    return res.send({ code: 404, message: "Missing ids" });
  }
});

/**
 * @swagger
 * /data/eval:
 *   post:
 *     summary: Thực thi script
 *     description: Thực thi script tùy chỉnh với keys và arguments
 *     tags: [Advanced]
 *     parameters:
 *       - $ref: '#/components/parameters/pidHeader'
 *       - $ref: '#/components/parameters/moduleHeader'
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/EvalRequest'
 *     responses:
 *       200:
 *         description: Thực thi thành công
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: "Success"
 *                 data:
 *                   type: object
 *                   description: Kết quả thực thi
 *       404:
 *         description: Lỗi thực thi
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.post("/eval", async (req, res) => {
  const { pid, module } = req.headers;
  const { script, keys, args } = req.body;
  const _moduleRepo = new crudDA(`data:${pid}:${module}`);
  try {
    const results = await _moduleRepo.eval(script, { arguments: args, keys: keys });
    return res.status(200).send({ data: results, code: 200, message: "Success" });
  } catch (error: any) {
    return res.send({ code: 404, message: error?.message });
  }
});

router.post("/testReward", async (req, res) => {
  const { shopid, id, customerid, status, code } = req.headers as any;
  _reward.fnFilial(shopid, id, customerid, status, code);
});

/**
 * @swagger
 * /data/action:
 *   post:
 *     summary: Thực hiện thao tác dữ liệu
 *     description: Thêm, sửa, nhân bản hoặc xóa dữ liệu
 *     tags: [Data]
 *     parameters:
 *       - $ref: '#/components/parameters/pidHeader'
 *       - $ref: '#/components/parameters/moduleHeader'
 *       - $ref: '#/components/parameters/authorizationHeader'
 *       - in: query
 *         name: action
 *         schema:
 *           type: string
 *           enum: [add, edit, duplicate, delete]
 *         required: true
 *         description: Loại thao tác
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/ActionRequest'
 *     responses:
 *       200:
 *         description: Thao tác thành công
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 code:
 *                   type: integer
 *                   example: 200
 *                 message:
 *                   type: string
 *                   example: "Success"
 *                 data:
 *                   type: object
 *                   description: Dữ liệu sau khi thực hiện thao tác
 *       404:
 *         description: Lỗi thực hiện thao tác
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ErrorResponse'
 */
router.post("/action", async (req, res) => {
  const { authorization, pid, module } = req.headers;
  const _moduleRepo = new crudDA(`data:${pid}:${module}`);
  const { action } = req.query;
  const { data, ids } = req.body;
  switch (`${action}`.toLowerCase()) {
    case "add":
      try {
        if (data.some((e: any) => !e?.Id || Object.keys(e).some((p: string) => e[p] && typeof e[p] === "object"))) return res.send({ code: 404, message: "Wrong data type or missing Id" });
        await _moduleRepo.action("add", data);
        if (module == "Order") {
          console.log("---data add", data);
          data?.forEach((e: any) => {
            _reward.fnFilial(e.ShopId, e.Id, e.CustomerId, e.Status, e.Code);
          });
        }
        return res.status(200).json({ data: data, code: 200, message: "Success" });
      } catch (error) {
        return res.send({ code: 404, message: (error as any).message ?? "" });
      }
    case "edit":
      try {
        if (data.some((e: any) => !e?.Id || Object.keys(e).some((p: string) => e[p] && typeof e[p] === "object"))) return res.send({ code: 404, message: "Wrong data type or missing Id" });

        await _moduleRepo.action("edit", data);
        console.log("---data edit", data);
        if (module == "Order") {
          data?.forEach((e: any) => {
            _reward.fnFilial(e.ShopId, e.Id, e.CustomerId, e.Status, e.Code);
          });
        }
        return res.status(200).json({ data: data, code: 200, message: "Success" });
      } catch (error) {
        return res.send({ code: 404, message: (error as any).message ?? "" });
      }
    case "duplicate":
      try {
        const currentData = await _moduleRepo.getBylistId(ids);
        await _moduleRepo.action(
          "add",
          currentData.map((e: any) => ({ ...e, Id: randomGID(), DateCreated: Date.now(), Name: `${e.Name} - Copy` }))
        );
        return res.status(200).json({ data: data, code: 200, message: "Success" });
      } catch (error) {
        return res.send({ code: 404, message: (error as any).message ?? "" });
      }
    case "delete":
      try {
        await _moduleRepo.delete(ids);
        const _relRepo = new crudDA(`setting:${pid}:rel`);
        _relRepo.search(1, 1000, `@TablePK:{${module}} @DeleteFK:{true}`, { RETURN: ["TableFK"] }).then(async (res) => {
          if (res.data?.length) {
            for (const item of res.data) {
              const _moduleFKRepo = new crudDA(`data:${pid}:${item.TableFK}`);
              _moduleFKRepo
                .aggregate({
                  searchRaw: `@${module === item.TableFK ? "Parent" : module}Id:{${ids.join(" | ")}}`,
                  query: `GROUPBY 1 @Id`,
                })
                .then((res) => {
                  if (res?.code !== 404) {
                    const childrenIds = res.filter((e: any) => Array.isArray(e) && e[1]?.length).map((e: any) => e[1]);
                    _moduleFKRepo.delete(childrenIds);
                  }
                });
            }
          }
        });
        return res.status(200).json({ data: ids, code: 200, message: "Success" });
      } catch (error) {
        return res.send({ code: 404, message: (error as any).message ?? "" });
      }
    default:
      return res.send({ code: 404, message: "Invalid action" });
  }
});

const _settingModule = ["chart", "card", "form", "view", "report"];
router.post("/:settingModule/action", async (req, res) => {
  const { settingModule } = req.params;
  if (_settingModule.includes(settingModule)) {
    const { pid } = req.headers;
    var _moduleRepo = new crudDA(`data:${pid}:data_${settingModule}`);
    const { action } = req.query;
    const { data, ids } = req.body;
    switch (action) {
      case "add":
        try {
          await _moduleRepo.action("add", data);
          return res.status(200).json({ data: data, code: 200, message: "Success" });
        } catch (error) {
          return res.send({ code: 404, message: (error as any).message ?? "" });
        }
      case "edit":
        try {
          await _moduleRepo.action("add", data);
          return res.status(200).json({ data: data, code: 200, message: "Success" });
        } catch (error) {
          return res.send({ code: 404, message: (error as any).message ?? "" });
        }
      case "delete":
        if (ids?.length) {
          try {
            await _moduleRepo.delete(ids);
            return res.status(200).json({ data: ids, code: 200, message: "Success" });
          } catch (error) {
            return res.send({ code: 404, message: (error as any).message ?? "" });
          }
        } else return res.send({ code: 404, message: "Missing ids!" });
      default:
        return res.send({ code: 404, message: "Invalid action" });
    }
  } else return res.status(404).send({ code: 404, message: "Page not found" });
});

router.post("/:settingModule/getListSimple", async (req, res) => {
  const { settingModule } = req.params;
  if (_settingModule.includes(settingModule)) {
    const { pid } = req.headers;
    const _moduleRepo = new crudDA(`data:${pid}:data_${settingModule}`);
    const { searchRaw, page, size, returns, sortby } = req.body;
    const results = await _moduleRepo.search(page, size, searchRaw, { RETURN: returns, SORTBY: sortby });
    if (results.code !== 404) {
      return res.status(200).json({ data: results.data, totalCount: results.count, code: 200, message: "Success" });
    } else return res.send({ code: 404, message: results.message });
  } else return res.status(404).send({ code: 404, message: "Page not found" });
});

router.post("/:settingModule/getByIds", async (req, res) => {
  const { settingModule } = req.params;
  if (_settingModule.includes(settingModule)) {
    const { pid } = req.headers;
    const _moduleRepo = new crudDA(`data:${pid}:data_${settingModule}`);
    const { ids } = req.body;
    if (ids?.length) {
      const results = await _moduleRepo.getBylistId(ids as any);
      return res.status(200).json({ data: results, code: 200, message: "Success" });
    } else {
      return res.send({ code: 404, message: "Missing ids" });
    }
  } else return res.status(404).send({ code: 404, message: "Page not found" });
});

async function aggregateData(props: { pid: string; module: string; sortby?: Array<{ prop: string; direction: string }>; searchRaw?: string; filter?: string; page?: number; size?: number; returns?: Array<string> }) {
  const _sortBy: Array<{ prop: string; direction: string }> = props.sortby ?? [{ prop: "DateCreated", direction: "DESC" }];
  const _moduleRepo = new crudDA(`data:${props.pid}:${props.module}`);
  const _tbColRepo = new crudDA(`setting:${props.pid}:column`);
  let _queryCols = await _tbColRepo.aggregate({
    searchRaw: `@TableName:{${props.module}} (-@Name:{Id | Name | DateCreated})`,
    query: `LOAD 3 @Id @Name @Query APPLY exists(@Query) AS __exist FILTER (@__exist == 1) APPLY strlen(@Query) AS qlength FILTER (@qlength > 0)`,
  });
  let _query = "";
  if (_queryCols?.code !== 404 && _queryCols.shift() && _queryCols.length) {
    const _queryList = _queryCols.map((e: Array<string>) => {
      let _jsonItem: { [p: string]: any } = {};
      for (let i = 0; i < e.length; i += 2) {
        if (e[i] === "$") {
          let _parseJson = {};
          try {
            _parseJson = JSON.parse(e[i + 1]);
          } catch (error) {
            console.log("parse $ failed");
          }
          _jsonItem = { ..._jsonItem, ..._parseJson };
        } else {
          _jsonItem[e[i]] = e[i + 1];
        }
      }
      return _jsonItem;
    });
    _query = `${_queryList.map((e: any) => `APPLY ${e.Query} AS ${e.Name}`).join(" ")} `;
  }
  let _size = props.size ?? 10000;
  let _page = props.page ?? 1;
  const finalQuery = `LOAD * ` + (props.filter ? `${props.filter} ` : "") + _query + `SORTBY ${_sortBy.length * 2} ${_sortBy.map((e: any) => `@${e.prop} ${e.direction ?? "ASC"}`).join(" ")} LIMIT ${(_page - 1) * _size} ${_size}`;
  const results = await _moduleRepo.aggregate({
    searchRaw: props.searchRaw,
    query: finalQuery,
  });
  if (results?.code !== 404) {
    const _totalCount = results.shift();
    if (_totalCount) {
      const _data = results.map((e: Array<any>) => {
        let _jsonItem: { [p: string]: any } = {};
        for (let i = 0; i < e.length; i += 2) {
          if (e[i] === "$") {
            let _parseJson: { [p: string]: any } = {};
            try {
              _parseJson = JSON.parse(e[i + 1]);
            } catch (error) {
              console.log("parse $ failed");
            }
            if (props.returns?.length) {
              Object.keys(_parseJson).forEach((p: string) => {
                if (!props.returns!.includes(p)) delete _parseJson[p];
              });
            }
            _jsonItem = { ..._jsonItem, ..._parseJson };
          } else if (!props.returns?.length || props.returns.includes(e[i])) {
            _jsonItem[e[i]] = e[i + 1];
          }
        }
        return _jsonItem;
      });
      return { data: _data, totalCount: _totalCount, code: 200, message: "Success" };
    } else return { data: [], totalCount: 0, code: 200, message: "Success" };
  } else return results;
}

export const mapGroupData = async (pid: string, module: string, query: string, searchRaw?: string) => {
  const mRepo = new crudDA(`data:${pid}:${module}`);
  const results = await mRepo.aggregate({ searchRaw: searchRaw ?? "*", query: query });
  if (results?.code !== 404) {
    const _totalCount = results.shift();
    if (_totalCount) {
      const _data = results.map((e: Array<any>) => {
        let _jsonItem: { [p: string]: any } = {};
        for (let i = 0; i < e.length; i += 2) {
          if (e[i] === "$") {
            let _parseJson = {};
            try {
              _parseJson = JSON.parse(e[i + 1]);
            } catch (error) {
              console.log("parse $ failed");
            }
            _jsonItem = { ..._jsonItem, ..._parseJson };
          } else {
            _jsonItem[e[i]] = e[i + 1];
          }
        }
        return _jsonItem;
      });
      return { data: _data, totalCount: _totalCount, code: 200, message: "Success" };
    } else {
      return { data: [], totalCount: 0, code: 200, message: "Success" };
    }
  } else {
    return { code: 404, message: results.message ?? "group error" };
  }
};

export default router;
